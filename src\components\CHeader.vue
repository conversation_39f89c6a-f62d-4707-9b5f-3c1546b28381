<!-- 顶部标题 -->
<template>
  <header class="header"></header>
  <!-- 图片展示区域 -->
  <div class="image-display">
    <div class="display-item">
      <div class="icon-container">
        <img src="@/assets/img/sd_icon.png" alt="园区总能耗图标" class="icon" />
      </div>
      <div class="content">
        <span class="label">园区总能耗</span>
        <span class="value">1000 kwh</span>
      </div>
    </div>
    <div class="display-item">
      <div class="icon-container">
        <img src="@/assets/img/sd_icon.png" alt="单位面积能耗图标" class="icon" />
      </div>
      <div class="content">
        <span class="label">单位面积能耗</span>
        <span class="value">100 kwh/m³</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 静态图片展示组件，无需额外逻辑
</script>

<style lang="scss" scoped>
.header {
  position: absolute;
  margin: 0 12px;
  top: 12px;
  width: calc(100% - 24px);
  height: 87px;
  background: url('@/assets/img/head_bg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40px;
  font-weight: 800;
  letter-spacing: 12px;
  color: rgba(230, 239, 253);
}

.image-display {
  position: absolute;
  top: 120px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 100px;
  align-items: center;
  justify-content: center;
}

.display-item {
  display: flex;
  align-items: center;
  gap: 15px;

  padding: 10px 30px;
  min-width: 300px;
  height: 60px;
  position: relative;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
}

.icon {
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 5px;
  color: #fff;
    background: url('@/assets/img/sd_bottom.png') no-repeat center center;
  background-size: contain;
}

.label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.value {
  font-size: 18px;
  font-weight: 600;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}
</style>
